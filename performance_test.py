"""
Performance test script for the optimized template parser.

This script compares the performance of different optimization approaches:
1. Single-threaded with original batching
2. Single-threaded with optimized batching
3. Multi-threaded with optimized batching

Run this script to see the performance improvements.

TODO: reset db before each test and not using dryrun to cauculate real time. Becareful not to delete all data.

"""

import time
import logging
from template_parser import TemplateParser

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def create_test_data():
    """Create test data for performance testing."""

    # Medium-sized test template
    template = template = """data=<data>;
databf=ts_backfill(data, <bf_days>);
signal=<sign>group_neutralize(databf, <group>);
ts_signal=<ts_op>(signal, <op_days>);
<op>(ts_signal)
"""

    # Reduced parameter space for faster testing
    parameter_space = {
        "<sign>": ["", "-"],
        "<bf_days>": ["21", "63", "252"],
        "<op_days>": ["1", "21", "63", "252"],
        "<group>": ["sector", "industry", "subindustry"],
        "<op>": ["rank", "zscore", "windsorize"],
        "<ts_op>": ["ts_delta", "ts_zscore", "ts_mean"],
        "<data>": [
            "mdl77_2rel5yfwdep",
            "mdl77_2garpanalystmodel_qgp_vfpriceratio",
            "mdl77_2400_yen",
            "mdl77_2garpanalystmodel_qgp_capeff",
            "mdl77_2deepvaluefactor_curep",
            "mdl77_2400_chg12msip",
        ],
    }

    settings_space = {
        'region': ['USA'],
        'universe': ['TOP3000'],
        'decay': [5, 10, 15],
        'neutralization': ['SUBINDUSTRY'],
        'truncation': [0.08],
    }

    return template, parameter_space, settings_space


def calculate_expected_combinations(parameter_space, settings_space):
    """Calculate expected number of combinations."""
    param_combinations = 1
    for values in parameter_space.values():
        param_combinations *= len(values)

    settings_combinations = 1
    for values in settings_space.values():
        settings_combinations *= len(values)

    return param_combinations * settings_combinations


def run_performance_test(test_name, parser, template, parameter_space, settings_space):
    """Run a single performance test."""
    print(f"\n{'='*60}")
    print(f"🧪 {test_name}")
    print(f"{'='*60}")

    expected = calculate_expected_combinations(parameter_space, settings_space)
    print(f"📊 Expected combinations: {expected:,}")

    start_time = time.time()

    try:
        template_id, alpha_count = parser.parse_template(template, parameter_space, settings_space)
        end_time = time.time()
        duration = end_time - start_time

        print(f"✅ Test completed successfully!")
        print(f"📈 Alphas generated: {alpha_count:,}")
        print(f"⏱️  Duration: {duration:.2f} seconds")
        print(f"🚀 Rate: {alpha_count/duration:.1f} alphas/second")
        print(f"💾 Template ID: {template_id}")

        return {
            'success': True,
            'duration': duration,
            'alpha_count': alpha_count,
            'rate': alpha_count/duration,
            'template_id': template_id
        }

    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time

        print(f"❌ Test failed after {duration:.2f} seconds")
        print(f"🚨 Error: {e}")
        logger.error(f"Performance test failed: {e}", exc_info=True)

        return {
            'success': False,
            'duration': duration,
            'error': str(e)
        }


def main():
    """Run comprehensive performance tests."""
    print("🚀 TEMPLATE PARSER PERFORMANCE TESTS")
    print("="*80)

    # Create test data
    template, parameter_space, settings_space = create_test_data()
    expected = calculate_expected_combinations(parameter_space, settings_space)

    # Calculate individual combinations correctly
    param_combinations = 1
    for values in parameter_space.values():
        param_combinations *= len(values)

    settings_combinations = 1
    for values in settings_space.values():
        settings_combinations *= len(values)

    print(f"📋 Test Configuration:")
    print(f"   - Parameter combinations: {param_combinations:,}")
    print(f"   - Settings combinations: {settings_combinations:,}")
    print(f"   - Total expected: {expected:,}")

    results = {}

    # Test 1: Single-threaded with optimized batching (small batch) - DRY RUN
    print(f"\n🔄 Running Test 1...")
    parser1 = TemplateParser(batch_size=50, max_workers=1, use_threading=False, dry_run=True)
    results['single_small_batch'] = run_performance_test(
        "Single-threaded (Small Batch: 50) - DRY RUN",
        parser1, template, parameter_space, settings_space
    )

    # Test 2: Single-threaded with optimized batching (large batch) - DRY RUN
    print(f"\n🔄 Running Test 2...")
    parser2 = TemplateParser(batch_size=200, max_workers=1, use_threading=False, dry_run=True)
    results['single_large_batch'] = run_performance_test(
        "Single-threaded (Large Batch: 200) - DRY RUN",
        parser2, template, parameter_space, settings_space
    )

    # Test 3: Multi-threaded with 2 workers - DRY RUN
    print(f"\n🔄 Running Test 3...")
    parser3 = TemplateParser(batch_size=100, max_workers=2, use_threading=True, dry_run=True)
    results['multi_2_workers'] = run_performance_test(
        "Multi-threaded (2 Workers, Batch: 100) - DRY RUN",
        parser3, template, parameter_space, settings_space
    )

    # Test 4: Multi-threaded with 4 workers - DRY RUN
    print(f"\n🔄 Running Test 4...")
    parser4 = TemplateParser(batch_size=100, max_workers=4, use_threading=True, dry_run=True)
    results['multi_4_workers'] = run_performance_test(
        "Multi-threaded (4 Workers, Batch: 100) - DRY RUN",
        parser4, template, parameter_space, settings_space
    )

    # Summary
    print(f"\n{'='*80}")
    print("📊 PERFORMANCE SUMMARY")
    print(f"{'='*80}")

    successful_tests = {k: v for k, v in results.items() if v['success']}

    if successful_tests:
        print(f"{'Test':<30} {'Duration':<12} {'Rate':<15} {'Alphas':<10}")
        print(f"{'-'*30} {'-'*12} {'-'*15} {'-'*10}")

        for test_name, result in successful_tests.items():
            print(f"{test_name:<30} {result['duration']:<12.2f} {result['rate']:<15.1f} {result['alpha_count']:<10,}")

        # Find best performance
        best_test = max(successful_tests.items(), key=lambda x: x[1]['rate'])
        print(f"\n🏆 Best Performance: {best_test[0]}")
        print(f"   Rate: {best_test[1]['rate']:.1f} alphas/second")
        print(f"   Duration: {best_test[1]['duration']:.2f} seconds")

        # Calculate improvement
        baseline = successful_tests.get('single_small_batch')
        if baseline and best_test[0] != 'single_small_batch':
            improvement = (best_test[1]['rate'] / baseline['rate'] - 1) * 100
            print(f"   Improvement: {improvement:.1f}% faster than baseline")

    failed_tests = {k: v for k, v in results.items() if not v['success']}
    if failed_tests:
        print(f"\n❌ Failed Tests: {len(failed_tests)}")
        for test_name, result in failed_tests.items():
            print(f"   - {test_name}: {result['error']}")

    print(f"\n{'='*80}")
    print("🎯 Performance test completed!")
    print(f"{'='*80}")


if __name__ == "__main__":
    main()
