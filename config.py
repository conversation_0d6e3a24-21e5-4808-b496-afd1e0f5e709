"""
Configuration module for BrainSpace session management system.
Handles environment variables and configuration settings.
"""

import os
from typing import List
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Config:
    """Configuration class for session management system."""

    # Telegram Bot Configuration
    TG_BOT_TOKEN: str = os.getenv("tg_bot_token", "")
    TG_CHAT_ID: str = os.getenv("tg_chat_id", "")

    # Brain API Configuration
    BRAIN_USER: str = os.getenv("BRAIN_USER", "")
    BRAIN_PASSWORD: str = os.getenv("BRAIN_PASSWORD", "")
    BRAIN_API_BASE_URL: str = "https://api.worldquantbrain.com"

    # Database Configuration
    DB_HOST: str = os.getenv("DB_HOST", "localhost")
    DB_DATABASE: str = os.getenv("DB_DATABASE", "brainspace")
    DB_USER: str = os.getenv("DB_USER", "postgres")
    DB_PASSWORD: str = os.getenv("DB_PASSWORD", "password")
    DB_PORT: int = int(os.getenv("DB_PORT", "5432"))

    # Session Configuration
    SESSION_FILE_PATH: str = os.getenv("SESSION_FILE_PATH", "session_data.json")
    SESSION_EXPIRY_HOURS: int = int(os.getenv("SESSION_EXPIRY_HOURS", "4"))

    # Notification Configuration
    NOTIFY_BEFORE_EXPIRY_MINUTES: List[int] = [
        int(x.strip()) for x in os.getenv("NOTIFY_BEFORE_EXPIRY_MINUTES", "60,30").split(",")
    ]

    # Retry Configuration
    MAX_RETRIES: int = int(os.getenv("MAX_RETRIES", "3"))
    RETRY_DELAY_SECONDS: int = int(os.getenv("RETRY_DELAY_SECONDS", "5"))

    # API Request Configuration
    REQUEST_TIMEOUT: int = int(os.getenv("REQUEST_TIMEOUT", "30"))

    # Simulator Configuration
    SIMULATOR_CONCURRENCY: int = int(os.getenv("SIMULATOR_CONCURRENCY") or "5")
    SIMULATOR_BATCH_SIZE: int = int(os.getenv("SIMULATOR_BATCH_SIZE") or "10")

    @classmethod
    def validate_config(cls) -> List[str]:
        """
        Validate required configuration values.

        Returns:
            List of missing or invalid configuration keys.
        """
        errors = []

        if not cls.TG_BOT_TOKEN:
            errors.append("tg_bot_token is required")

        if not cls.TG_CHAT_ID:
            errors.append("tg_chat_id is required")

        if not cls.BRAIN_USER:
            errors.append("BRAIN_USER is required")

        if not cls.BRAIN_PASSWORD:
            errors.append("BRAIN_PASSWORD is required")

        if cls.SESSION_EXPIRY_HOURS <= 0:
            errors.append("SESSION_EXPIRY_HOURS must be positive")

        if cls.MAX_RETRIES < 0:
            errors.append("MAX_RETRIES must be non-negative")

        if cls.SIMULATOR_CONCURRENCY <= 0:
            errors.append("SIMULATOR_CONCURRENCY must be positive")

        if cls.SIMULATOR_BATCH_SIZE <= 0:
            errors.append("SIMULATOR_BATCH_SIZE must be positive")

        return errors

    @classmethod
    def get_session_expiry_seconds(cls) -> int:
        """Get session expiry time in seconds."""
        return cls.SESSION_EXPIRY_HOURS * 3600


# Global config instance
config = Config()
