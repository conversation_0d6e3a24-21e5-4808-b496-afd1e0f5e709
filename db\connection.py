"""
Database connection management for BrainSpace.
"""

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from config import config


def get_database_url() -> str:
    """Get PostgreSQL database URL from environment variables."""
    db_host = config.DB_HOST
    db_database = config.DB_DATABASE
    db_user = config.DB_USER
    db_password = config.DB_PASSWORD
    db_port = config.DB_PORT

    return f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_database}"


_engine = None
_Session = None

def get_database_engine():
    """Get SQLAlchemy database engine, creating it if it doesn't exist."""
    global _engine
    if _engine is None:
        _engine = create_engine(get_database_url())
    return _engine

def get_database_session():
    """Get SQLAlchemy database session, creating the factory if it doesn't exist."""
    global _Session
    if _Session is None:
        engine = get_database_engine()
        _Session = sessionmaker(bind=engine)
    return _Session()