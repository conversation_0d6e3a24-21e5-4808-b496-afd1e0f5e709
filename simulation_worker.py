import logging
import time
from datetime import datetime
from typing import List
from concurrent.futures import Thread<PERSON>ool<PERSON>xecutor

from sqlalchemy import text
from sqlalchemy.orm import sessionmaker

from config import config
from db import get_database_engine
from models import Alpha, AlphaStatus
from brain_api_client import BrainAP<PERSON>lient
from session.session_manager import <PERSON><PERSON><PERSON><PERSON>
from typing import Optional
import requests

logger = logging.getLogger(__name__)

class SimulationWorker:
    """
    Manages the continuous loop for fetching and submitting alphas for simulation.
    """

    def __init__(self, session_manager: SessionManager, brain_api_client: BrainAPIClient):
        self.session_manager = session_manager
        self.brain_api_client = brain_api_client
        self.db_engine = get_database_engine()
        self.Session = sessionmaker(bind=self.db_engine)
        self.running = False

    def start(self):
        """Starts the simulation worker loop."""
        logger.info("Simulation worker started.")
        self.running = True
        while self.running:
            try:
                self._process_pending_alphas()
            except Exception as e:
                logger.error(f"Error in simulation worker loop: {e}", exc_info=True)
            finally:
                # Use retry delay for polling interval
                time.sleep(config.RETRY_DELAY_SECONDS)

    def stop(self):
        """Stops the simulation worker loop."""
        logger.info("Simulation worker stopping.")
        self.running = False

    def _process_pending_alphas(self):
        """Fetches pending alphas and submits them for simulation."""
        if not self.running:
            return

        pending_alphas = self._fetch_pending_alphas()
        if not pending_alphas:
            logger.debug("No pending alphas to simulate.")
            return

        logger.info(f"Found {len(pending_alphas)} alphas with status 'none'.")
        self._submit_alphas_in_batches(pending_alphas)

    def _fetch_pending_alphas(self) -> List[Alpha]:
        """Fetches and claims alphas with status 'none' from the database atomically."""
        if not self.running:
            return []
        with self.Session() as session:
            with session.begin():
                # Use FOR UPDATE SKIP LOCKED to select and lock rows atomically
                result = session.execute(
                    text("""SELECT id, alpha, settings, status, simulation_id, result, error, created_at, simulate_at, last_check, result_at
                             FROM alpha
                             WHERE status = :status_none
                             LIMIT :batch_size
                             FOR UPDATE SKIP LOCKED"""
                    ),
                    {"status_none": AlphaStatus.NONE.value, "batch_size": config.SIMULATOR_BATCH_SIZE}
                ).fetchall()

                if not result:
                    return []

                alphas = [Alpha(**row._mapping) for row in result]
                alpha_ids = [alpha.id for alpha in alphas]

                # Atomically update status to 'simulating' to claim the jobs
                session.execute(
                    text("UPDATE alpha SET status = :status, simulate_at = :now WHERE id = ANY(:alpha_ids)"),
                    {"status": AlphaStatus.SIMULATING.value, "now": datetime.now(), "alpha_ids": alpha_ids}
                )

                return alphas

    def _submit_alphas_in_batches(self, alphas: List[Alpha]):
        """Submits alphas to the Brain API in batches using a thread pool."""
        if not self.running:
            return

        with ThreadPoolExecutor(max_workers=config.SIMULATOR_CONCURRENCY) as executor:
            futures = [executor.submit(
                self._submit_single_alpha, alpha) for alpha in alphas]
            for future in futures:
                # Wait for each submission to complete (or handle results if needed)
                future.result()

    def _submit_single_alpha(self, alpha: Alpha):
        """Submits a single alpha to the Brain API."""
        if not self.running:
            return

        logger.info(f"Attempting to submit alpha ID: {alpha.id}")
        token = self.session_manager.get_session_token()
        if not token:
            logger.error(
                f"Could not get session token for alpha ID: {alpha.id}. Skipping submission.")
            self._update_alpha_status(
                alpha, AlphaStatus.ERROR, error_message="No active session token.")
            return

        try:
            payload = {
                'type': 'REGULAR',
                'settings': alpha.settings,
                'regular': alpha.alpha
            }
            response = self.brain_api_client.make_authenticated_request(
                'POST', '/simulations', token, json=payload
            )
            response.raise_for_status()  # Raise an exception for HTTP errors

            simulation_id = response.headers.get(
                'Location', '').strip('/').split('/')[-1]

            if simulation_id:
                self._update_alpha_status(
                    alpha, AlphaStatus.SIMULATING, simulation_id=simulation_id)
                logger.info(
                    f"Alpha ID: {alpha.id} submitted successfully with simulation ID: {simulation_id}")
            else:
                try:
                    error_message = response.json().get(
                        "message", "Unknown API error during submission.")
                except requests.exceptions.JSONDecodeError:
                    error_message = f"Failed to get simulation ID from a 2xx response. Status: {response.status_code}, Body: {response.text[:200]}"
                self._update_alpha_status(
                    alpha, AlphaStatus.ERROR, error_message=error_message)
                logger.error(
                    f"Alpha ID: {alpha.id} failed to get simulation ID: {error_message}")

        except requests.exceptions.RequestException as e:
            error_message = f"API request failed: {e}"
            self._update_alpha_status(
                alpha, AlphaStatus.ERROR, error_message=error_message)
            logger.error(
                f"Alpha ID: {alpha.id} submission failed: {error_message}", exc_info=True)
        except Exception as e:
            error_message = f"Unexpected error during alpha submission: {e}"
            self._update_alpha_status(
                alpha, AlphaStatus.ERROR, error_message=error_message)
            logger.error(
                f"Alpha ID: {alpha.id} submission failed: {error_message}", exc_info=True)

    def _update_alpha_status(self, alpha: Alpha, status: AlphaStatus, simulation_id: Optional[str] = None, error_message: Optional[str] = None):
        """Updates the status of an alpha in the database."""
        with self.Session() as session:
            with session.begin():
                update_query = text("""
                    UPDATE alpha
                    SET status = :status,
                        simulation_id = :simulation_id,
                        error = :error_message
                    WHERE id = :alpha_id
                """)
                session.execute(
                    update_query,
                    {
                        "status": status.value,
                        "simulation_id": simulation_id,
                        "error_message": error_message,
                        "alpha_id": alpha.id
                    },
                )
        logger.debug(
            f"Alpha ID: {alpha.id} status updated to {status.value}")
