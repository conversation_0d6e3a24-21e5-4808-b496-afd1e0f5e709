"""
Template parser for generating alpha variants from templates and parameter spaces.

This module implements the Alpha Space Parser component as described in the PRD.
It generates all possible alpha code variants from templates and parameter spaces,
saves them to the database with proper deduplication.
"""

import logging
import itertools
import json
import threading
import time
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import Dict, List, Any, Tuple, Generator
from datetime import datetime

from tqdm import tqdm
from sqlalchemy import text, bindparam
from sqlalchemy.dialects import postgresql
import sqlalchemy
from sqlalchemy.orm import sessionmaker

from db import get_database_engine
from models import AlphaStatus

logger = logging.getLogger(__name__)


class TemplateParser:
    """
    Parses templates with parameter spaces to generate alpha variants.

    Handles:
    - String replacement to generate all permutations
    - Settings space expansion with defaults
    - Database storage with deduplication
    - Batch processing to avoid memory issues
    """

    # Default settings as specified in the API documentation
    DEFAULT_SETTINGS = {
        'instrumentType': 'EQUITY',
        'region': 'USA',
        'universe': 'TOP3000',
        'delay': 1,
        'decay': 15,
        'neutralization': 'SUBINDUSTRY',
        'truncation': 0.08,
        'maxTrade': 'ON',
        'pasteurization': 'ON',
        'testPeriod': 'P1Y6M',
        'unitHandling': 'VERIFY',
        'nanHandling': 'OFF',
        'language': 'FASTEXPR',
        'visualization': False,
    }

    def __init__(self, batch_size: int = 100, max_workers: int = 4, use_threading: bool = True, dry_run: bool = False):
        """
        Initialize the template parser.

        Args:
            batch_size: Number of alphas to process in each batch to avoid memory issues
            max_workers: Maximum number of worker threads for parallel processing
            use_threading: Whether to use multi-threading for database operations
            dry_run: If True, skip database commits (for testing/performance measurement)
        """
        self.batch_size = batch_size
        self.max_workers = max_workers
        self.use_threading = use_threading
        self.dry_run = dry_run

        # Configure database engine with optimized settings
        self.db_engine = get_database_engine()
        if use_threading:
            # Ensure we have enough connections for multi-threading
            self.db_engine = self.db_engine.execution_options(
                pool_size=max_workers + 2,
                max_overflow=max_workers * 2,
                # Optimize for performance
                pool_pre_ping=True,  # Verify connections before use
                pool_recycle=3600,   # Recycle connections every hour
            )

        self.Session = sessionmaker(bind=self.db_engine)

        # Thread-safe progress tracking
        self._progress_lock = threading.Lock()
        self._total_generated = 0

    def parse_template(
        self,
        template: str,
        parameter_space: Dict[str, List[str]],
        settings_space: Dict[str, List[Any]]
    ) -> Tuple[int, int]:
        """
        Parse a template with parameter and settings spaces to generate alpha variants.

        Args:
            template: Template string with placeholders (e.g., "<data>", "<op>")
            parameter_space: Dictionary mapping placeholders to possible values
            settings_space: Dictionary mapping setting names to possible values

        Returns:
            Tuple of (template_id, number_of_alphas_generated)
        """
        template = "\n".join(line.strip()
                             for line in template.strip().split('\n'))

        # Calculate total expected combinations for progress bar
        param_combinations = 1
        for values in parameter_space.values():
            param_combinations *= len(values)

        settings_combinations = 1
        for values in settings_space.values():
            settings_combinations *= len(values)

        total_expected = param_combinations * settings_combinations

        print(f"🚀 Starting template parsing")
        print(f"📝 Template length: {len(template)} characters")
        print(f"🔧 Parameter space: {list(parameter_space.keys())} ({param_combinations} combinations)")
        print(f"⚙️  Settings space: {list(settings_space.keys())} ({settings_combinations} combinations)")
        print(f"📊 Total expected alphas: {total_expected:,}")

        if self.dry_run:
            # In dry run mode, handle everything in a single transaction
            with self.Session() as session:
                session.begin()
                try:
                    print(
                        "🧪 DRY RUN: Processing with database simulation (single transaction)")
                    total_generated = self._process_all_batches(
                        template, parameter_space, settings_space, total_expected, session=session
                    )
                    session.rollback()  # Rollback at the end
                    print("🧪 DRY RUN: All operations rolled back successfully")
                    template_id = -1  # Use fake ID for dry run
                except Exception as e:
                    session.rollback()
                    logger.error(
                        f"Dry run processing failed: {e}", exc_info=True)
                    raise e
        else:
            # Normal mode processing
            template_id = self._save_template(
                template, parameter_space, settings_space)
            print(f"💾 Template saved with ID: {template_id}")

            total_generated = self._process_all_batches(
                template, parameter_space, settings_space, total_expected, template_id=template_id
            )

        print(f"✅ Template parsing completed!")
        print(f"📈 Total alphas generated: {total_generated:,}")
        print(
            f"🎯 Deduplication rate: {((total_expected - total_generated) / total_expected * 100):.1f}%")
        return template_id, total_generated

    def _process_all_batches(
        self,
        template: str,
        parameter_space: Dict[str, List[str]],
        settings_space: Dict[str, List[Any]],
        total_expected: int,
        template_id: int = None,
        session=None
    ) -> int:
        """
        Process all alpha batches with progress tracking, choosing between single-threaded
        and multi-threaded execution based on configuration.

        This method contains the core processing logic that can be used by both
        normal mode and dry run mode. The outer methods are responsible for
        managing transactions (committing or rolling back).

        Args:
            template: Template string
            parameter_space: Parameter space dictionary
            settings_space: Settings space dictionary
            total_expected: Total expected number of alphas for progress tracking
            template_id: Template ID for database references (None for dry run)
            session: Database session to use (for dry run mode)

        Returns:
            Total number of alphas generated
        """
        # For dry run mode, we need to create a template_id within the session
        if self.dry_run and session and template_id is None:
            result = session.execute(
                text("""
                    INSERT INTO template (template, parameter_space, settings_space, created_at)
                    VALUES (:template, CAST(:parameter_space AS jsonb), CAST(:settings_space AS jsonb), :created_at)
                    RETURNING id
                """),
                {
                    "template": template,
                    "parameter_space": json.dumps(parameter_space),
                    "settings_space": json.dumps(settings_space),
                    "created_at": datetime.now()
                }
            )
            template_id = result.scalar()
            print(
                f"💾 Template saved with ID: {template_id} (will be rolled back)")

        with tqdm(total=total_expected, desc="🔄 Processing alphas", unit="alpha") as pbar:
            if self.use_threading and self.max_workers > 1:
                # Multi-threaded processing
                return self._process_batches_threaded(
                    template, parameter_space, settings_space, template_id, pbar
                )
            else:
                # Single-threaded processing
                return self._process_batches_single_threaded(
                    template, parameter_space, settings_space, template_id, pbar
                )

    def _process_batches_single_threaded(
        self,
        template: str,
        parameter_space: Dict[str, List[str]],
        settings_space: Dict[str, List[Any]],
        template_id: int,
        pbar: tqdm
    ) -> int:
        """
        Process alpha batches in single-threaded mode with progress tracking.
        """
        total_generated = 0
        batch_count = 0

        for alpha_batch in self._generate_alpha_batches(template, parameter_space, settings_space):
            batch_count += 1
            batch_size = len(alpha_batch)

            generated_count = self._save_alpha_batch_optimized(
                alpha_batch, template_id)
            total_generated += generated_count

            # Update progress bar
            pbar.update(batch_size)
            pbar.set_postfix({
                'batch': batch_count,
                'saved': f"{generated_count}/{batch_size}",
                'total_saved': total_generated
            })

        return total_generated

    def _process_batches_threaded(
        self,
        template: str,
        parameter_space: Dict[str, List[str]],
        settings_space: Dict[str, List[Any]],
        template_id: int,
        pbar: tqdm
    ) -> int:
        """
        Process alpha batches using a simplified ThreadPoolExecutor pattern.

        This replaces the complex producer-consumer queue pattern with a simpler
        approach that directly submits batch processing tasks to the thread pool.

        Args:
            template: Template string
            parameter_space: Parameter space dictionary
            settings_space: Settings space dictionary
            template_id: Template ID for database references
            pbar: Progress bar for updates

        Returns:
            Total number of alphas generated
        """
        total_generated = 0

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Create a generator for alpha batches
            alpha_batches = self._generate_alpha_batches(
                template, parameter_space, settings_space)

            # Submit each batch to the thread pool for processing
            futures = {
                executor.submit(self._save_alpha_batch_optimized, batch, template_id): batch
                for batch in alpha_batches
            }

            for future in as_completed(futures):
                batch = futures[future]
                try:
                    # Get the result (number of saved alphas)
                    generated_count = future.result()
                    total_generated += generated_count

                    # Update progress bar (thread-safe)
                    with self._progress_lock:
                        pbar.update(len(batch))
                        pbar.set_postfix({
                            'workers': self.max_workers,
                            'saved_in_batch': f"{generated_count}/{len(batch)}",
                            'total_saved': total_generated
                        })

                except Exception as e:
                    logger.error(
                        f"A batch failed to process: {e}", exc_info=True)

        return total_generated

    def _save_template(
        self,
        template: str,
        parameter_space: Dict[str, List[str]],
        settings_space: Dict[str, List[Any]]
    ) -> int:
        """Save template to database and return its ID."""
        with self.Session() as session:
            # Start a transaction (will be committed or rolled back based on dry_run)
            session.begin()

            try:
                if self.dry_run:
                    print("🧪 DRY RUN: Simulating template save (will rollback)")

                # Execute the INSERT operation (same for both modes)
                result = session.execute(
                    text("""
                        INSERT INTO template (template, parameter_space, settings_space, created_at)
                        VALUES (:template, CAST(:parameter_space AS jsonb), CAST(:settings_space AS jsonb), :created_at)
                        RETURNING id
                    """),
                    {
                        "template": template,
                        "parameter_space": json.dumps(parameter_space),
                        "settings_space": json.dumps(settings_space),
                        "created_at": datetime.now()
                    }
                )
                template_id = result.scalar()

                if self.dry_run:
                    # In dry run mode, rollback to prevent commit
                    session.rollback()
                else:
                    # In normal mode, commit the transaction
                    session.commit()

                return template_id

            except Exception as e:
                session.rollback()
                raise e

    def _generate_alpha_stream(
        self,
        template: str,
        parameter_space: Dict[str, List[str]],
        settings_space: Dict[str, List[Any]]
    ) -> Generator[Tuple[str, Dict], None, None]:
        """
        Generate alpha variants one at a time using lazy evaluation.

        This generator yields individual (alpha_code, settings) tuples without
        loading all combinations into memory at once.

        Yields:
            Tuple of (alpha_code, full_settings)
        """
        # Prepare parameter space
        param_keys = list(parameter_space.keys())
        param_values = [parameter_space[key] for key in param_keys]

        # Prepare settings space
        settings_keys = list(settings_space.keys())
        settings_values = [settings_space[key] for key in settings_keys]

        # Generate combinations lazily using itertools.product
        for param_combo in itertools.product(*param_values):
            # Create parameter substitution mapping
            param_mapping = dict(zip(param_keys, param_combo))

            # Generate alpha code by replacing placeholders
            alpha_code = template
            for placeholder, value in param_mapping.items():
                alpha_code = alpha_code.replace(placeholder, str(value))

            # Generate all settings combinations for this alpha
            for settings_combo in itertools.product(*settings_values):
                # Create full settings by merging with defaults
                full_settings = self.DEFAULT_SETTINGS.copy()
                settings_mapping = dict(zip(settings_keys, settings_combo))
                full_settings.update(settings_mapping)

                yield (alpha_code, full_settings)

    def _generate_alpha_batches(
        self,
        template: str,
        parameter_space: Dict[str, List[str]],
        settings_space: Dict[str, List[Any]]
    ) -> Generator[List[Tuple[str, Dict]], None, None]:
        """
        Generate alpha variants in batches using lazy evaluation.

        Uses the alpha stream generator and itertools.islice for memory-efficient
        batch processing without loading all combinations into memory.

        Yields:
            List of (alpha_code, settings) tuples (batches)
        """
        alpha_stream = self._generate_alpha_stream(
            template, parameter_space, settings_space)

        while True:
            # Use itertools.islice to get the next batch without loading everything
            batch = list(itertools.islice(alpha_stream, self.batch_size))
            if not batch:
                break
            yield batch

    def _save_alpha_batch_optimized(self, alpha_batch: List[Tuple[str, Dict]], template_id: int) -> int:
        """
        Highly optimized batch save using true bulk INSERT operations.

        This method performs a single bulk INSERT for alphas and another for alpha_references,
        dramatically reducing database round-trips from batch_size queries to just 2 queries.
        Uses PostgreSQL's UNNEST function for efficient bulk operations with ON CONFLICT.

        Args:
            alpha_batch: List of (alpha_code, settings) tuples
            template_id: ID of the template these alphas were generated from

        Returns:
            Number of alphas actually saved (after deduplication)
        """
        if not alpha_batch:
            return 0

        with self.Session() as session:
            with session.begin():
                try:
                    if self.dry_run:
                        # In dry run mode, simulate the operation without actual database changes
                        # Just return the batch size as if all were saved (for performance testing)
                        saved_count = len(alpha_batch)
                        session.rollback()
                        return saved_count

                    # Prepare data for bulk insert
                    alpha_codes = []
                    settings_json = []
                    created_at = datetime.now()

                    for alpha_code, settings in alpha_batch:
                        alpha_codes.append(alpha_code)
                        settings_json.append(json.dumps(settings))

                    # Step 1: Bulk insert alphas with ON CONFLICT DO NOTHING
                    # This single query handles all alphas in the batch
                    bulk_alpha_insert_query = text("""
                        WITH input_data AS (
                            SELECT
                                unnest(:alpha_codes) as alpha,
                                unnest(:settings_json) as settings,
                                CAST(:status AS alpha_status) as status,
                                :created_at as created_at
                        ),
                        inserted_alphas AS (
                            INSERT INTO alpha (alpha, settings, status, created_at)
                            SELECT alpha, settings, status, created_at FROM input_data
                            ON CONFLICT (alpha, settings) DO NOTHING
                            RETURNING id, alpha, settings
                        )
                        SELECT COUNT(*) as inserted_count FROM inserted_alphas
                    """).bindparams(
                        bindparam("alpha_codes", type_=postgresql.ARRAY(
                            sqlalchemy.String)),
                        bindparam("settings_json",
                                  type_=postgresql.ARRAY(postgresql.JSONB)),
                        # cast happens in SQL
                        bindparam("status", type_=sqlalchemy.String),
                        bindparam("created_at", type_=sqlalchemy.DateTime),
                    )

                    result = session.execute(
                        bulk_alpha_insert_query,
                        {
                            "alpha_codes": alpha_codes,
                            "settings_json": settings_json,
                            "status": "none",   # cast happens in SQL
                            "created_at": created_at,
                        },
                    )

                    saved_count = result.scalar() or 0

                    # Step 2: Create alpha_reference entries for all alphas in the batch
                    # This handles both newly inserted and existing alphas
                    # We use a single query that gets alpha IDs and inserts references in one operation
                    bulk_reference_insert_query = text("""
                        WITH input_data AS (
                            SELECT
                                unnest(:alpha_codes) as alpha,
                                unnest(:settings_json) as settings
                        ),
                        alpha_ids AS (
                            SELECT a.id as alpha_id
                            FROM alpha a
                            INNER JOIN input_data i
                                ON a.alpha = i.alpha
                            AND a.settings = i.settings
                        )
                        INSERT INTO alpha_reference (alpha_id, template_id)
                        SELECT alpha_id, :template_id
                        FROM alpha_ids
                        ON CONFLICT (alpha_id, template_id) DO NOTHING
                    """).bindparams(
                        bindparam("alpha_codes", type_=postgresql.ARRAY(
                            sqlalchemy.String)),
                        bindparam("settings_json",
                                  type_=postgresql.ARRAY(postgresql.JSONB)),
                        bindparam("template_id", type_=sqlalchemy.Integer),
                    )


                    session.execute(
                        bulk_reference_insert_query,
                        {
                            "alpha_codes": alpha_codes,
                            "settings_json": settings_json,
                            "template_id": template_id,
                        },
                    )

                    return saved_count

                except Exception as e:
                    session.rollback()
                    logger.error(f"Batch save failed: {e}", exc_info=True)
                    raise e


def parse_from_file(
    file_path: str,
    batch_size: int = 100,
    max_workers: int = 4,
    use_threading: bool = True,
    dry_run: bool = False
) -> Tuple[int, int]:
    """
    Parse template from the file with optimized performance settings.

    Args:
        file_path: Path to the file containing template, parameter_space, and settings_space
        batch_size: Number of alphas to process in each batch
        max_workers: Maximum number of worker threads for parallel processing
        use_threading: Whether to use multi-threading for database operations
        dry_run: If True, skip database commits (for testing/performance measurement)

    Returns:
        Tuple of (template_id, number_of_alphas_generated)
    """
    # Import the file to get template, parameter_space, and settings_space
    import importlib.util
    import sys

    spec = importlib.util.spec_from_file_location("template_module", file_path)
    template_module = importlib.util.module_from_spec(spec)
    sys.modules["template_module"] = template_module
    spec.loader.exec_module(template_module)

    template = template_module.template
    parameter_space = template_module.parameter_space
    settings_space = template_module.settings_space

    print(f"🔧 Performance Settings:")
    print(f"   - Batch size: {batch_size}")
    print(f"   - Max workers: {max_workers}")
    print(f"   - Threading: {'Enabled' if use_threading else 'Disabled'}")
    print(f"   - Dry run: {'Enabled' if dry_run else 'Disabled'}")

    parser = TemplateParser(
        batch_size=batch_size,
        max_workers=max_workers,
        use_threading=use_threading,
        dry_run=dry_run
    )
    return parser.parse_template(template, parameter_space, settings_space)


if __name__ == "__main__":
    """
    Main entry point for running the template parser.
    Can be run as a standalone script with performance optimizations.
    """
    import argparse

    # Parse command line arguments for performance tuning
    parser = argparse.ArgumentParser(
        description='Optimized Template Parser for Alpha Generation')
    parser.add_argument('--batch-size', type=int, default=100,
                        help='Number of alphas per batch (default: 100)')
    parser.add_argument('--max-workers', type=int, default=2,
                        help='Maximum number of worker threads (default: 2)')
    parser.add_argument('--no-threading', action='store_true',
                        help='Disable multi-threading (use single-threaded mode)')
    parser.add_argument('--dry-run', action='store_true',
                        help='Skip database commits (for testing/performance measurement)')
    parser.add_argument('-f', '--file', type=str, default="alpha_space/example.py",
                        help='Path to alpha template file (default: alpha_space/example.py)')

    args = parser.parse_args()
    print(args)

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    print("=" * 80)
    print("🚀 OPTIMIZED ALPHA TEMPLATE PARSER")
    print("=" * 80)
    print(f"📁 Template file: {args.file}")
    print(f"📦 Batch size: {args.batch_size}")
    print(f"🧵 Max workers: {args.max_workers}")
    print(f"⚡ Threading: {'Disabled' if args.no_threading else 'Enabled'}")
    print(f"🧪 Dry run: {'Enabled' if args.dry_run else 'Disabled'}")
    print("=" * 80)

    start_time = time.time()

    try:
        template_id, alpha_count = parse_from_file(
            file_path=args.file,
            batch_size=args.batch_size,
            max_workers=args.max_workers,
            use_threading=not args.no_threading,
            dry_run=args.dry_run
        )

        end_time = time.time()
        duration = end_time - start_time

        print("=" * 80)
        print("✅ TEMPLATE PARSING COMPLETED SUCCESSFULLY!")
        print("=" * 80)
        print(f"📊 Template ID: {template_id}")
        print(f"🎯 Alphas generated: {alpha_count:,}")
        print(f"⏱️  Total time: {duration:.2f} seconds")
        print(f"🚀 Processing rate: {alpha_count/duration:.1f} alphas/second")
        print("=" * 80)

    except Exception as e:
        logger.error(f"Template parsing failed: {e}", exc_info=True)
        print(f"❌ Error: {e}")
        exit(1)
